/**
 * TypeScript environment variable declarations
 * This file provides type safety for process.env variables
 */

declare namespace NodeJS {
  interface ProcessEnv {
    // Database Configuration
    DATABASE_PATH?: string;

    // Server Configuration
    PORT?: string;
    NODE_ENV?: 'development' | 'production' | 'test';

    // WooCommerce API Configuration
    WOOCOMMERCE_URL?: string;
    WOOCOMMERCE_CONSUMER_KEY?: string;
    WOOCOMMERCE_CONSUMER_SECRET?: string;

    // External Server Configuration (for XML sync)
    EXTERNAL_SERVER_URL?: string;
    EXTERNAL_SERVER_USERNAME?: string;
    EXTERNAL_SERVER_PASSWORD?: string;

    // Sync Configuration
    SYNC_INTERVAL_MINUTES?: string;
    ENABLE_AUTO_SYNC?: string;

    // Cron Configuration
    CRON_RZA_FULL_SYNC?: string;
    CRON_RZA_LAGERSTAND_SYNC?: string;
    CRON_WOOCOMMERCE_ORDER_SYNC?: string;

    // Logging Configuration
    LOG_LEVEL?: 'error' | 'warn' | 'info' | 'debug';
    LOG_DIR?: string;
  }
}
