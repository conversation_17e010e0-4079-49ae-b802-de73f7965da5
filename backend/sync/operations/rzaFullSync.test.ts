/**
 * Unit tests for RZA Full Sync operation
 * Tests the complete sync process with mocked dependencies
 */

import { performRZAFullSync, RZAFullSyncConfig } from './rzaFullSync';
import { WooCommerceService } from '../../services/woocommerce';
import * as fileDiscovery from '../rza/export/fileDiscovery';
import * as xmlParser from '../rza/export/xmlParser';
import * as wooCommerceSync from '../rza/export/wooCommerceSync';

// Mock dependencies
jest.mock('../../services/woocommerce');
jest.mock('../rza/export/fileDiscovery');
jest.mock('../rza/export/xmlParser');
jest.mock('../rza/export/wooCommerceSync');
jest.mock('fs/promises');

describe('RZA Full Sync', () => {
  let mockWooCommerceService: jest.Mocked<WooCommerceService>;
  let mockFindLatestRZAFullSyncFile: jest.MockedFunction<typeof fileDiscovery.findLatestRZAFullSyncFile>;
  let mockValidateRZAFile: jest.MockedFunction<typeof fileDiscovery.validateRZAFile>;
  let mockParseRZAXML: jest.MockedFunction<typeof xmlParser.parseRZAXML>;
  let mockUpsertProductsToWooCommerce: jest.MockedFunction<typeof wooCommerceSync.upsertProductsToWooCommerce>;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Create mock WooCommerce service
    mockWooCommerceService = {
      getProducts: jest.fn(),
      createProduct: jest.fn(),
      updateProduct: jest.fn(),
      healthCheck: jest.fn()
    } as any;

    // Mock file discovery functions
    mockFindLatestRZAFullSyncFile = fileDiscovery.findLatestRZAFullSyncFile as jest.MockedFunction<typeof fileDiscovery.findLatestRZAFullSyncFile>;
    mockValidateRZAFile = fileDiscovery.validateRZAFile as jest.MockedFunction<typeof fileDiscovery.validateRZAFile>;

    // Mock XML parser
    mockParseRZAXML = xmlParser.parseRZAXML as jest.MockedFunction<typeof xmlParser.parseRZAXML>;

    // Mock WooCommerce sync functions
    mockUpsertProductsToWooCommerce = wooCommerceSync.upsertProductsToWooCommerce as jest.MockedFunction<typeof wooCommerceSync.upsertProductsToWooCommerce>;

    // Mock fs.readFile
    const fs = require('fs/promises');
    fs.readFile = jest.fn().mockResolvedValue('<xml>test content</xml>');
  });

  describe('performRZAFullSync', () => {
    it('should successfully complete a full sync', async () => {
      // Arrange
      const mockFileInfo = {
        filePath: '/test/artcustord-20250801140000_1.xml',
        fileName: 'artcustord-20250801140000_1.xml',
        timestamp: new Date('2025-08-01T14:00:00Z'),
        sequenceNumber: 1,
        fileSize: 1024
      };

      const mockRzaData = {
        articles: [
          {
            ordernumber: 'TEST-001',
            rzaArtikelID: 1,
            artGroupID: 1,
            artSubGroupID: 1,
            categories: [1],
            name: 'Test Product',
            ean: '1234567890',
            barcode: 'TEST001',
            unitID: 'stk',
            instock: 10,
            stock: 15,
            weight: 1.5,
            active: 1,
            onlineshopstatus: 1,
            description_long: 'Test description',
            tax: 20,
            taxRates: [],
            suppliernumbers: [],
            shippingtime: '',
            fields: [],
            translations: [],
            prices: [{ price: 19.99, pricegroup: 'VK-Preis', from: 1 }],
            textilartikel: {}
          }
        ],
        customers: [],
        orders: [],
        groupDefinition: { artGroups: [], artSubGroups: [] },
        categories: [],
        countries: []
      };

      const mockUpsertResult = {
        totalProcessed: 1,
        created: 1,
        updated: 0,
        skipped: 0,
        failed: 0,
        results: [
          {
            sku: 'TEST-001',
            success: true,
            action: 'created' as const,
            productId: 123
          }
        ],
        errors: []
      };

      // Setup mocks
      mockFindLatestRZAFullSyncFile.mockResolvedValue(mockFileInfo);
      mockValidateRZAFile.mockResolvedValue(true);
      mockParseRZAXML.mockResolvedValue(mockRzaData);
      mockUpsertProductsToWooCommerce.mockResolvedValue(mockUpsertResult);

      // Act
      const result = await performRZAFullSync(mockWooCommerceService);

      // Assert
      expect(result.success).toBe(true);
      expect(result.stats.fileProcessed).toBe('artcustord-20250801140000_1.xml');
      expect(result.stats.articlesFound).toBe(1);
      expect(result.stats.articlesProcessed).toBe(1);
      expect(result.stats.articlesCreated).toBe(1);
      expect(result.stats.articlesUpdated).toBe(0);
      expect(result.stats.errors).toHaveLength(0);
      expect(result.processingTimeMs).toBeGreaterThan(0);

      // Verify function calls
      expect(mockFindLatestRZAFullSyncFile).toHaveBeenCalled();
      expect(mockValidateRZAFile).toHaveBeenCalledWith(mockFileInfo.filePath);
      expect(mockParseRZAXML).toHaveBeenCalledWith('<xml>test content</xml>');
      expect(mockUpsertProductsToWooCommerce).toHaveBeenCalledWith(
        mockWooCommerceService,
        expect.any(Array),
        expect.any(Object)
      );
    });

    it('should handle WooCommerce health check failure', async () => {
      // Act
      const result = await performRZAFullSync(mockWooCommerceService);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('WooCommerce service is not healthy');
      expect(result.stats.errors).toHaveLength(1);
      expect(result.stats.errors[0]).toContain('WooCommerce service is not healthy');

      // Verify that subsequent functions were not called
      expect(mockFindLatestRZAFullSyncFile).not.toHaveBeenCalled();
    });

    it('should handle no RZA files found', async () => {
      mockFindLatestRZAFullSyncFile.mockResolvedValue(null);

      // Act
      const result = await performRZAFullSync(mockWooCommerceService);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('No valid RZA full sync files found');
      expect(result.stats.errors).toHaveLength(1);
    });

    it('should handle invalid RZA file', async () => {
      // Arrange
      const mockFileInfo = {
        filePath: '/test/invalid.xml',
        fileName: 'invalid.xml',
        timestamp: new Date(),
        sequenceNumber: 1,
        fileSize: 0
      };

      mockFindLatestRZAFullSyncFile.mockResolvedValue(mockFileInfo);
      mockValidateRZAFile.mockResolvedValue(false);

      // Act
      const result = await performRZAFullSync(mockWooCommerceService);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('RZA file is not valid or readable');
      expect(result.stats.errors).toHaveLength(1);
    });

    it('should handle XML parsing errors', async () => {
      // Arrange
      const mockFileInfo = {
        filePath: '/test/artcustord-20250801140000_1.xml',
        fileName: 'artcustord-20250801140000_1.xml',
        timestamp: new Date(),
        sequenceNumber: 1,
        fileSize: 1024
      };

      mockFindLatestRZAFullSyncFile.mockResolvedValue(mockFileInfo);
      mockValidateRZAFile.mockResolvedValue(true);
      mockParseRZAXML.mockRejectedValue(new Error('Invalid XML format'));

      // Act
      const result = await performRZAFullSync(mockWooCommerceService);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Invalid XML format');
      expect(result.stats.errors).toHaveLength(1);
    });

    it('should handle empty articles in XML', async () => {
      // Arrange
      const mockFileInfo = {
        filePath: '/test/artcustord-20250801140000_1.xml',
        fileName: 'artcustord-20250801140000_1.xml',
        timestamp: new Date(),
        sequenceNumber: 1,
        fileSize: 1024
      };

      const mockRzaData = {
        articles: [],
        customers: [],
        orders: [],
        groupDefinition: { artGroups: [], artSubGroups: [] },
        categories: [],
        countries: []
      };

      mockFindLatestRZAFullSyncFile.mockResolvedValue(mockFileInfo);
      mockValidateRZAFile.mockResolvedValue(true);
      mockParseRZAXML.mockResolvedValue(mockRzaData);

      // Act
      const result = await performRZAFullSync(mockWooCommerceService);

      // Assert
      expect(result.success).toBe(true);
      expect(result.message).toContain('no articles to process');
      expect(result.stats.articlesFound).toBe(0);
      expect(result.stats.articlesProcessed).toBe(0);

      // Verify upsert was not called
      expect(mockUpsertProductsToWooCommerce).not.toHaveBeenCalled();
    });

    it('should skip health check when configured', async () => {
      // Arrange
      const config: RZAFullSyncConfig = {
      };

      const mockFileInfo = {
        filePath: '/test/artcustord-20250801140000_1.xml',
        fileName: 'artcustord-20250801140000_1.xml',
        timestamp: new Date(),
        sequenceNumber: 1,
        fileSize: 1024
      };

      const mockRzaData = {
        articles: [],
        customers: [],
        orders: [],
        groupDefinition: { artGroups: [], artSubGroups: [] },
        categories: [],
        countries: []
      };

      mockFindLatestRZAFullSyncFile.mockResolvedValue(mockFileInfo);
      mockValidateRZAFile.mockResolvedValue(true);
      mockParseRZAXML.mockResolvedValue(mockRzaData);

      // Act
      const result = await performRZAFullSync(mockWooCommerceService, config);

      // Assert
      expect(result.success).toBe(true);
    });

    it('should handle partial sync failures gracefully', async () => {
      // Arrange
      const mockFileInfo = {
        filePath: '/test/artcustord-20250801140000_1.xml',
        fileName: 'artcustord-20250801140000_1.xml',
        timestamp: new Date(),
        sequenceNumber: 1,
        fileSize: 1024
      };

      const mockRzaData = {
        articles: [
          {
            ordernumber: 'TEST-001',
            rzaArtikelID: 1,
            artGroupID: 1,
            artSubGroupID: 1,
            categories: [],
            name: 'Test Product',
            ean: '',
            barcode: '',
            unitID: 'stk',
            instock: 10,
            stock: 15,
            weight: 1.5,
            active: 1,
            onlineshopstatus: 1,
            description_long: '',
            tax: 20,
            taxRates: [],
            suppliernumbers: [],
            shippingtime: '',
            fields: [],
            translations: [],
            prices: [{ price: 19.99, pricegroup: 'VK-Preis', from: 1 }],
            textilartikel: {}
          }
        ],
        customers: [],
        orders: [],
        groupDefinition: { artGroups: [], artSubGroups: [] },
        categories: [],
        countries: []
      };

      const mockUpsertResult = {
        totalProcessed: 1,
        created: 0,
        updated: 1,
        skipped: 0,
        failed: 0,
        results: [
          {
            sku: 'TEST-001',
            success: true,
            action: 'updated' as const,
            productId: 123
          }
        ],
        errors: []
      };

      mockFindLatestRZAFullSyncFile.mockResolvedValue(mockFileInfo);
      mockValidateRZAFile.mockResolvedValue(true);
      mockParseRZAXML.mockResolvedValue(mockRzaData);
      mockUpsertProductsToWooCommerce.mockResolvedValue(mockUpsertResult);

      // Act
      const result = await performRZAFullSync(mockWooCommerceService);

      // Assert
      expect(result.success).toBe(true);
      expect(result.stats.articlesCreated).toBe(0);
      expect(result.stats.articlesUpdated).toBe(1);
      expect(result.message).toContain('Created: 0, Updated: 1, Failed: 0');
    });
  });

  describe('Error Handling', () => {
    it('should handle unexpected errors gracefully', async () => {
      // Act
      const result = await performRZAFullSync(mockWooCommerceService);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Network error');
      expect(result.stats.errors).toHaveLength(1);
      expect(result.processingTimeMs).toBeGreaterThan(0);
    });

    it('should handle file system errors', async () => {
      // Arrange
      mockFindLatestRZAFullSyncFile.mockRejectedValue(new Error('Permission denied'));

      // Act
      const result = await performRZAFullSync(mockWooCommerceService);

      // Assert
      expect(result.success).toBe(false);
      expect(result.message).toContain('Permission denied');
      expect(result.stats.errors).toHaveLength(1);
    });
  });

  describe('Configuration Options', () => {
    it('should pass custom upsert configuration', async () => {
      // Arrange
      const customConfig: RZAFullSyncConfig = {
        includeInactiveProducts: true,
        upsertConfig: {
          batchSize: 5,
          delayBetweenBatches: 500,
          skipExistingProducts: true,
          validateBeforeUpsert: false
        }
      };

      const mockFileInfo = {
        filePath: '/test/artcustord-20250801140000_1.xml',
        fileName: 'artcustord-20250801140000_1.xml',
        timestamp: new Date(),
        sequenceNumber: 1,
        fileSize: 1024
      };

      const mockRzaData = {
        articles: [
          {
            ordernumber: 'TEST-001',
            rzaArtikelID: 1,
            artGroupID: 1,
            artSubGroupID: 1,
            categories: [],
            name: 'Test Product',
            ean: '',
            barcode: '',
            unitID: 'stk',
            instock: 10,
            stock: 15,
            weight: 1.5,
            active: 0, // Inactive product
            onlineshopstatus: 1,
            description_long: '',
            tax: 20,
            taxRates: [],
            suppliernumbers: [],
            shippingtime: '',
            fields: [],
            translations: [],
            prices: [{ price: 19.99, pricegroup: 'VK-Preis', from: 1 }],
            textilartikel: {}
          }
        ],
        customers: [],
        orders: [],
        groupDefinition: { artGroups: [], artSubGroups: [] },
        categories: [],
        countries: []
      };

      const mockUpsertResult = {
        totalProcessed: 1,
        created: 0,
        updated: 0,
        skipped: 1,
        failed: 0,
        results: [
          {
            sku: 'TEST-001',
            success: true,
            action: 'skipped' as const,
            productId: 123
          }
        ],
        errors: []
      };

      mockFindLatestRZAFullSyncFile.mockResolvedValue(mockFileInfo);
      mockValidateRZAFile.mockResolvedValue(true);
      mockParseRZAXML.mockResolvedValue(mockRzaData);
      mockUpsertProductsToWooCommerce.mockResolvedValue(mockUpsertResult);

      // Act
      const result = await performRZAFullSync(mockWooCommerceService, customConfig);

      // Assert
      expect(result.success).toBe(true);
      expect(mockUpsertProductsToWooCommerce).toHaveBeenCalledWith(
        mockWooCommerceService,
        expect.any(Array),
        customConfig.upsertConfig
      );
    });
  });
});
