/**
 * RZA Full Sync Operation
 * Handles the complete synchronization of products from RZA to WooCommerce
 */

import { promises as fs } from 'fs';
import { createModuleLogger } from '../../infrastructure/logger';
import { WooCommerceService } from '../../services/woocommerce';
import { findLatestRZAFullSyncFile, validateRZAFile } from '../rza/export/fileDiscovery';
import { parseRZAXML } from '../rza/export/xmlParser';
import { mapRzaArticlesToWooProducts } from '../rza/export/mappers/articles.mapper';
import { upsertProductsToWooCommerce, UpsertConfig } from '../rza/export/wooCommerceSync';

const logger = createModuleLogger('rza-full-sync');

/**
 * Result of RZA full sync operation
 */
export interface RZAFullSyncResult {
  success: boolean;
  message: string;
  stats: {
    fileProcessed: string;
    articlesFound: number;
    articlesProcessed: number;
    articlesCreated: number;
    articlesUpdated: number;
    categoriesProcessed: number;
    errors: string[];
  };
  processingTimeMs: number;
}

/**
 * Configuration for RZA full sync operation
 */
export interface RZAFullSyncConfig {
  syncDirectory?: string;
  upsertConfig?: Partial<UpsertConfig>;
  includeInactiveProducts?: boolean;
}

/**
 * Default configuration for RZA full sync
 */
export const DEFAULT_RZA_FULL_SYNC_CONFIG: RZAFullSyncConfig = {
  includeInactiveProducts: false,
  upsertConfig: {
    batchSize: 10,
    delayBetweenBatches: 1000,
    skipExistingProducts: false,
    validateBeforeUpsert: true
  }
};

/**
 * Performs a full sync of all products from RZA to WooCommerce
 * This includes product data, prices, categories, and stock information
 *
 * @param wooCommerceService - WooCommerce service instance for API operations
 * @param config - Configuration options for the sync operation
 * @returns Promise resolving to sync result with statistics
 */
export async function performRZAFullSync(
  wooCommerceService: WooCommerceService,
  config: RZAFullSyncConfig = {}
): Promise<RZAFullSyncResult> {
  const startTime = Date.now();
  const syncConfig = { ...DEFAULT_RZA_FULL_SYNC_CONFIG, ...config };

  const result: RZAFullSyncResult = {
    success: false,
    message: '',
    stats: {
      fileProcessed: '',
      articlesFound: 0,
      articlesProcessed: 0,
      articlesCreated: 0,
      articlesUpdated: 0,
      categoriesProcessed: 0,
      errors: []
    },
    processingTimeMs: 0
  };

  try {
    logger.info('Starting RZA full sync operation');

    // Step 2: Find latest RZA file
    logger.info('Searching for latest RZA full sync file');
    const fileInfo = await findLatestRZAFullSyncFile(syncConfig.syncDirectory);
    if (!fileInfo) {
      throw new Error('No valid RZA full sync files found');
    }

    logger.info(`Found RZA file: ${fileInfo.fileName}`, {
      timestamp: fileInfo.timestamp.toISOString(),
      size: fileInfo.fileSize
    });

    // Step 3: Validate file
    const isValidFile = await validateRZAFile(fileInfo.filePath);
    if (!isValidFile) {
      throw new Error(`RZA file is not valid or readable: ${fileInfo.filePath}`);
    }

    result.stats.fileProcessed = fileInfo.fileName;

    // Step 4: Parse XML file
    logger.info('Parsing RZA XML file');
    const xmlContent = await fs.readFile(fileInfo.filePath, 'utf-8');
    const rzaData = await parseRZAXML(xmlContent);

    result.stats.articlesFound = rzaData.articles.length;
    logger.info(`Parsed RZA data: ${rzaData.articles.length} articles, ${rzaData.categories.length} categories`);

    if (rzaData.articles.length === 0) {
      logger.warn('No articles found in RZA file');
      result.success = true;
      result.message = 'Sync completed successfully - no articles to process';
      result.processingTimeMs = Date.now() - startTime;
      return result;
    }

    // Step 5: Transform RZA data to WooCommerce format
    logger.info('Transforming RZA data to WooCommerce format');
    const wooProducts = mapRzaArticlesToWooProducts(
      rzaData.articles,
      rzaData.categories,
      {
        includeInactiveProducts: syncConfig.includeInactiveProducts || false
      }
    );

    logger.info(`Mapped ${wooProducts.length} products for WooCommerce`);

    if (wooProducts.length === 0) {
      logger.warn('No products to sync after filtering');
      result.success = true;
      result.message = 'Sync completed successfully - no products to sync after filtering';
      result.processingTimeMs = Date.now() - startTime;
      return result;
    }

    // Step 6: Upsert products to WooCommerce
    logger.info('Starting product upsert to WooCommerce');
    const upsertResult = await upsertProductsToWooCommerce(
      wooCommerceService,
      wooProducts,
      syncConfig.upsertConfig
    );

    // Update result statistics
    result.stats.articlesProcessed = upsertResult.totalProcessed;
    result.stats.articlesCreated = upsertResult.created;
    result.stats.articlesUpdated = upsertResult.updated;
    result.stats.errors = upsertResult.errors;

    // Determine overall success
    const hasErrors = upsertResult.failed > 0;
    result.success = !hasErrors || (upsertResult.created + upsertResult.updated) > 0;

    if (result.success) {
      result.message = `Sync completed successfully. Created: ${upsertResult.created}, Updated: ${upsertResult.updated}, Failed: ${upsertResult.failed}`;
    } else {
      result.message = `Sync completed with errors. Created: ${upsertResult.created}, Updated: ${upsertResult.updated}, Failed: ${upsertResult.failed}`;
    }

    logger.info('RZA full sync operation completed', {
      success: result.success,
      articlesProcessed: result.stats.articlesProcessed,
      created: result.stats.articlesCreated,
      updated: result.stats.articlesUpdated,
      errors: result.stats.errors.length
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('RZA full sync operation failed:', error);

    result.success = false;
    result.message = `Sync failed: ${errorMessage}`;
    result.stats.errors.push(errorMessage);
  }

  result.processingTimeMs = Date.now() - startTime;
  return result;
}
