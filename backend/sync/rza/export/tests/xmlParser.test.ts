/**
 * Unit tests for RZA XML parser
 */

import { parseRZAXML } from '../xmlParser';

describe('RZA XML Parser', () => {
  describe('parseRZAXML', () => {
    it('should parse valid RZA XML with articles', async () => {
      // Arrange
      const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <articles>
            <article>
              <ordernumber>TEST-001</ordernumber>
              <rzaArtikelID>123</rzaArtikelID>
              <artGroupID>1</artGroupID>
              <artSubGroupID>2</artSubGroupID>
              <categories>
                <category>10</category>
                <category>20</category>
              </categories>
              <name>Test Product</name>
              <ean>1234567890</ean>
              <barcode>TEST001</barcode>
              <unitID>stk</unitID>
              <instock>50</instock>
              <stock>100</stock>
              <weight>1.5</weight>
              <active>1</active>
              <onlineshopstatus>1</onlineshopstatus>
              <description_long><![CDATA[<p>Test description</p>]]></description_long>
              <tax>20.00</tax>
              <prices>
                <price>
                  <price>19.99</price>
                  <pricegroup>VK-Preis</pricegroup>
                  <from>1</from>
                </price>
                <price>
                  <price>17.99</price>
                  <pricegroup>VK-Preis</pricegroup>
                  <from>10</from>
                </price>
              </prices>
              <fields>
                <field fieldnumber="1">Field Value 1</field>
                <field fieldnumber="2">Field Value 2</field>
              </fields>
            </article>
          </articles>
          <categories>
            <category>
              <ID>10</ID>
              <ParentID>5</ParentID>
              <name>Category 1</name>
              <number>1</number>
            </category>
          </categories>
          <groupDefinition>
            <artGroups>
              <artGroup>
                <ID>1</ID>
                <number>1</number>
                <name>Group 1</name>
              </artGroup>
            </artGroups>
            <artSubGroups>
              <artSubGroup>
                <ID>2</ID>
                <number>2</number>
                <name>Sub Group 1</name>
              </artSubGroup>
            </artSubGroups>
          </groupDefinition>
        </root>`;

      // Act
      const result = await parseRZAXML(xmlContent);

      // Assert
      expect(result.articles).toHaveLength(1);

      const article = result.articles[0]!;
      expect(article.ordernumber).toBe('TEST-001');
      expect(article.rzaArtikelID).toBe(123);
      expect(article.artGroupID).toBe(1);
      expect(article.artSubGroupID).toBe(2);
      expect(article.categories).toEqual([10, 20]);
      expect(article.name).toBe('Test Product');
      expect(article.ean).toBe('1234567890');
      expect(article.barcode).toBe('TEST001');
      expect(article.unitID).toBe('stk');
      expect(article.instock).toBe(50);
      expect(article.stock).toBe(100);
      expect(article.weight).toBe(1.5);
      expect(article.active).toBe(1);
      expect(article.onlineshopstatus).toBe(1);
      expect(article.description_long).toBe('<p>Test description</p>');
      expect(article.tax).toBe(20);
      expect(article.prices).toHaveLength(2);
      expect(article.prices[0]!.price).toBe(19.99);
      expect(article.prices[0]!.pricegroup).toBe('VK-Preis');
      expect(article.prices[0]!.from).toBe(1);
      expect(article.fields).toHaveLength(2);
      expect(article.fields[0]!.fieldnumber).toBe(1);
      expect(article.fields[0]!.value).toBe('Field Value 1');

      expect(result.categories).toHaveLength(1);
      expect(result.categories[0]!.ID).toBe(10);
      expect(result.categories[0]!.ParentID).toBe(5);
      expect(result.categories[0]!.name).toBe('Category 1');

      expect(result.groupDefinition.artGroups).toHaveLength(1);
      expect(result.groupDefinition.artGroups[0]!.ID).toBe(1);
      expect(result.groupDefinition.artGroups[0]!.name).toBe('Group 1');

      expect(result.groupDefinition.artSubGroups).toHaveLength(1);
      expect(result.groupDefinition.artSubGroups[0]!.ID).toBe(2);
      expect(result.groupDefinition.artSubGroups[0]!.name).toBe('Sub Group 1');
    });

    it('should handle empty XML sections', async () => {
      // Arrange
      const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <articles></articles>
          <categories></categories>
          <groupDefinition>
            <artGroups></artGroups>
            <artSubGroups></artSubGroups>
          </groupDefinition>
        </root>`;

      // Act
      const result = await parseRZAXML(xmlContent);

      // Assert
      expect(result.articles).toHaveLength(0);
      expect(result.categories).toHaveLength(0);
      expect(result.groupDefinition.artGroups).toHaveLength(0);
      expect(result.groupDefinition.artSubGroups).toHaveLength(0);
    });

    it('should handle missing XML sections', async () => {
      // Arrange
      const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
        <root>
        </root>`;

      // Act
      const result = await parseRZAXML(xmlContent);

      // Assert
      expect(result.articles).toHaveLength(0);
      expect(result.categories).toHaveLength(0);
      expect(result.groupDefinition.artGroups).toHaveLength(0);
      expect(result.groupDefinition.artSubGroups).toHaveLength(0);
    });

    it('should skip articles with missing required fields', async () => {
      // Arrange
      const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <articles>
            <article>
              <ordernumber>TEST-001</ordernumber>
              <rzaArtikelID>123</rzaArtikelID>
              <name>Valid Article</name>
            </article>
            <article>
              <ordernumber>TEST-002</ordernumber>
              <!-- Missing rzaArtikelID -->
              <name>Invalid Article</name>
            </article>
            <article>
              <rzaArtikelID>125</rzaArtikelID>
              <!-- Missing ordernumber -->
              <name>Another Invalid Article</name>
            </article>
            <article>
              <ordernumber>TEST-004</ordernumber>
              <rzaArtikelID>126</rzaArtikelID>
              <!-- Missing name -->
            </article>
          </articles>
        </root>`;

      // Act
      const result = await parseRZAXML(xmlContent);

      // Assert
      expect(result.articles).toHaveLength(1);
      expect(result.articles[0]!.ordernumber).toBe('TEST-001');
    });

    it('should handle single article (not array)', async () => {
      // Arrange
      const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <articles>
            <article>
              <ordernumber>SINGLE-001</ordernumber>
              <rzaArtikelID>999</rzaArtikelID>
              <name>Single Article</name>
            </article>
          </articles>
        </root>`;

      // Act
      const result = await parseRZAXML(xmlContent);

      // Assert
      expect(result.articles).toHaveLength(1);
      expect(result.articles[0]!.ordernumber).toBe('SINGLE-001');
    });

    it('should handle default values for optional fields', async () => {
      // Arrange
      const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <articles>
            <article>
              <ordernumber>MINIMAL-001</ordernumber>
              <rzaArtikelID>777</rzaArtikelID>
              <name>Minimal Article</name>
              <!-- All other fields missing -->
            </article>
          </articles>
        </root>`;

      // Act
      const result = await parseRZAXML(xmlContent);

      // Assert
      expect(result.articles).toHaveLength(1);

      const article = result.articles[0]!;
      expect(article.ordernumber).toBe('MINIMAL-001');
      expect(article.rzaArtikelID).toBe(777);
      expect(article.name).toBe('Minimal Article');
      expect(article.artGroupID).toBe(0);
      expect(article.artSubGroupID).toBe(0);
      expect(article.categories).toEqual([]);
      expect(article.ean).toBe('');
      expect(article.barcode).toBe('');
      expect(article.unitID).toBe('stk');
      expect(article.instock).toBe(0);
      expect(article.stock).toBe(0);
      expect(article.weight).toBe(0);
      expect(article.active).toBe(0);
      expect(article.onlineshopstatus).toBe(0);
      expect(article.description_long).toBe('');
      expect(article.tax).toBe(0);
      expect(article.taxRates).toEqual([]);
      expect(article.suppliernumbers).toEqual([]);
      expect(article.shippingtime).toBe('');
      expect(article.fields).toEqual([]);
      expect(article.translations).toEqual([]);
      expect(article.prices).toEqual([]);
      expect(article.textilartikel).toEqual({});
    });

    it('should handle complex textile article data', async () => {
      // Arrange
      const xmlContent = `<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <articles>
            <article>
              <ordernumber>TEXTILE-001</ordernumber>
              <rzaArtikelID>888</rzaArtikelID>
              <name>Textile Article</name>
              <textilartikel>
                <kennzeichen>1</kennzeichen>
                <textilartikelID>999</textilartikelID>
                <groessentabelleID>10</groessentabelleID>
                <groessenID>5</groessenID>
                <farbtabelleID>20</farbtabelleID>
                <farbenID>15</farbenID>
                <saisonID>2025</saisonID>
                <preiszuschlag>5.00</preiszuschlag>
              </textilartikel>
            </article>
          </articles>
        </root>`;

      // Act
      const result = await parseRZAXML(xmlContent);

      // Assert
      expect(result.articles).toHaveLength(1);

      const article = result.articles[0]!;
      expect(article.textilartikel.kennzeichen).toBe('1');
      expect(article.textilartikel.textilartikelID).toBe('999');
      expect(article.textilartikel.groessentabelleID).toBe('10');
      expect(article.textilartikel.groessenID).toBe('5');
      expect(article.textilartikel.farbtabelleID).toBe('20');
      expect(article.textilartikel.farbenID).toBe('15');
      expect(article.textilartikel.saisonID).toBe('2025');
      expect(article.textilartikel.preiszuschlag).toBe('5.00');
    });

    it('should handle invalid XML gracefully', async () => {
      // Arrange
      const invalidXml = `<?xml version="1.0" encoding="UTF-8"?>
        <root>
          <articles>
            <article>
              <ordernumber>TEST-001</ordernumber>
              <!-- Missing closing tag -->
            </article>
        </root>`;

      // Act & Assert
      await expect(parseRZAXML(invalidXml)).rejects.toThrow('XML parsing failed');
    });

    it('should handle malformed XML structure', async () => {
      // Arrange
      const malformedXml = 'This is not XML at all';

      // Act & Assert
      await expect(parseRZAXML(malformedXml)).rejects.toThrow('XML parsing failed');
    });
  });
});
