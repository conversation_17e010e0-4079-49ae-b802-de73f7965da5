/**
 * Unit tests for RZA file discovery utilities
 */

import { promises as fs } from 'fs';
import path from 'path';
import { findLatestRZAFullSyncFile, listAllRZAFullSyncFiles, validateRZAFile } from '../fileDiscovery';

// Mock fs module
jest.mock('fs/promises');

describe('RZA File Discovery', () => {
  const mockFs = fs as jest.Mocked<typeof fs>;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('findLatestRZAFullSyncFile', () => {
    it('should find the latest RZA file', async () => {
      // Arrange
      const mockFiles = [
        'artcustord-20250801140000_1.xml',
        'artcustord-20250801150000_1.xml',
        'artcustord-20250801140000_2.xml',
        'other-file.xml'
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readdir.mockResolvedValue(mockFiles as any);
      mockFs.stat.mockImplementation((filePath: any) => {
        const fileName = path.basename(filePath);
        return Promise.resolve({
          isFile: () => fileName.endsWith('.xml'),
          size: 1024
        } as any);
      });

      // Act
      const result = await findLatestRZAFullSyncFile('/test/dir');

      // Assert
      expect(result).not.toBeNull();
      expect(result!.fileName).toBe('artcustord-20250801150000_1.xml');
      expect(result!.timestamp).toEqual(new Date(2025, 7, 1, 15, 0, 0)); // Month is 0-based
      expect(result!.sequenceNumber).toBe(1);
    });

    it('should return null when no valid files found', async () => {
      // Arrange
      const mockFiles = ['other-file.xml', 'invalid-name.xml'];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readdir.mockResolvedValue(mockFiles as any);
      mockFs.stat.mockResolvedValue({
        isFile: () => true,
        size: 1024
      } as any);

      // Act
      const result = await findLatestRZAFullSyncFile('/test/dir');

      // Assert
      expect(result).toBeNull();
    });

    it('should return null when directory does not exist', async () => {
      // Arrange
      mockFs.access.mockRejectedValue(new Error('Directory not found'));

      // Act
      const result = await findLatestRZAFullSyncFile('/nonexistent/dir');

      // Assert
      expect(result).toBeNull();
    });

    it('should handle file system errors', async () => {
      // Arrange
      mockFs.access.mockResolvedValue(undefined);
      mockFs.readdir.mockRejectedValue(new Error('Permission denied'));

      // Act & Assert
      await expect(findLatestRZAFullSyncFile('/test/dir')).rejects.toThrow('Permission denied');
    });

    it('should sort files by timestamp and sequence number correctly', async () => {
      // Arrange
      const mockFiles = [
        'artcustord-20250801140000_2.xml', // Same time, higher sequence
        'artcustord-20250801140000_1.xml', // Same time, lower sequence
        'artcustord-20250801150000_1.xml'  // Later time
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readdir.mockResolvedValue(mockFiles as any);
      mockFs.stat.mockResolvedValue({
        isFile: () => true,
        size: 1024
      } as any);

      // Act
      const result = await findLatestRZAFullSyncFile('/test/dir');

      // Assert
      expect(result!.fileName).toBe('artcustord-20250801150000_1.xml'); // Latest timestamp wins
    });
  });

  describe('listAllRZAFullSyncFiles', () => {
    it('should list all valid RZA files sorted by timestamp', async () => {
      // Arrange
      const mockFiles = [
        'artcustord-20250801140000_1.xml',
        'artcustord-20250801150000_1.xml',
        'invalid-file.xml'
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readdir.mockResolvedValue(mockFiles as any);
      mockFs.stat.mockResolvedValue({
        isFile: () => true,
        size: 1024
      } as any);

      // Act
      const result = await listAllRZAFullSyncFiles('/test/dir');

      // Assert
      expect(result).toHaveLength(2);
      expect(result[0].fileName).toBe('artcustord-20250801150000_1.xml'); // Newest first
      expect(result[1].fileName).toBe('artcustord-20250801140000_1.xml');
    });

    it('should return empty array when directory does not exist', async () => {
      // Arrange
      mockFs.access.mockRejectedValue(new Error('Directory not found'));

      // Act
      const result = await listAllRZAFullSyncFiles('/nonexistent/dir');

      // Assert
      expect(result).toEqual([]);
    });
  });

  describe('validateRZAFile', () => {
    it('should return true for valid file', async () => {
      // Arrange
      mockFs.stat.mockResolvedValue({
        isFile: () => true,
        size: 1024
      } as any);

      // Act
      const result = await validateRZAFile('/test/file.xml');

      // Assert
      expect(result).toBe(true);
    });

    it('should return false for empty file', async () => {
      // Arrange
      mockFs.stat.mockResolvedValue({
        isFile: () => true,
        size: 0
      } as any);

      // Act
      const result = await validateRZAFile('/test/empty.xml');

      // Assert
      expect(result).toBe(false);
    });

    it('should return false for directory', async () => {
      // Arrange
      mockFs.stat.mockResolvedValue({
        isFile: () => false,
        size: 1024
      } as any);

      // Act
      const result = await validateRZAFile('/test/directory');

      // Assert
      expect(result).toBe(false);
    });

    it('should return false when file does not exist', async () => {
      // Arrange
      mockFs.stat.mockRejectedValue(new Error('File not found'));

      // Act
      const result = await validateRZAFile('/test/nonexistent.xml');

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('Filename Parsing', () => {
    it('should parse valid RZA filenames correctly', async () => {
      // Arrange
      const validFiles = [
        'artcustord-20250801140000_1.xml',
        'artcustord-20251231235959_999.xml'
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readdir.mockResolvedValue(validFiles as any);
      mockFs.stat.mockResolvedValue({
        isFile: () => true,
        size: 1024
      } as any);

      // Act
      const result = await listAllRZAFullSyncFiles('/test/dir');

      // Assert
      expect(result).toHaveLength(2);
      expect(result[0].timestamp).toEqual(new Date(2025, 11, 31, 23, 59, 59)); // Latest
      expect(result[0].sequenceNumber).toBe(999);
      expect(result[1].timestamp).toEqual(new Date(2025, 7, 1, 14, 0, 0));
      expect(result[1].sequenceNumber).toBe(1);
    });

    it('should ignore files with invalid naming patterns', async () => {
      // Arrange
      const invalidFiles = [
        'artcustord-invalid_1.xml',
        'artcustord-20250801140000.xml', // Missing sequence
        'artcustord-20250801140000_0.xml', // Invalid sequence (0)
        'wrong-prefix-20250801140000_1.xml',
        'artcustord-20250801140000_1.txt' // Wrong extension
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readdir.mockResolvedValue(invalidFiles as any);
      mockFs.stat.mockResolvedValue({
        isFile: () => true,
        size: 1024
      } as any);

      // Act
      const result = await listAllRZAFullSyncFiles('/test/dir');

      // Assert
      expect(result).toHaveLength(0);
    });

    it('should handle edge cases in date parsing', async () => {
      // Arrange
      const edgeCaseFiles = [
        'artcustord-20250229140000_1.xml', // Invalid date (not leap year)
        'artcustord-20250132140000_1.xml', // Invalid day
        'artcustord-20251301140000_1.xml', // Invalid month
        'artcustord-20250801250000_1.xml', // Invalid hour
        'artcustord-20250801146000_1.xml', // Invalid minute
        'artcustord-20250801140060_1.xml'  // Invalid second
      ];

      mockFs.access.mockResolvedValue(undefined);
      mockFs.readdir.mockResolvedValue(edgeCaseFiles as any);
      mockFs.stat.mockResolvedValue({
        isFile: () => true,
        size: 1024
      } as any);

      // Act
      const result = await listAllRZAFullSyncFiles('/test/dir');

      // Assert
      expect(result).toHaveLength(0); // All should be filtered out as invalid
    });
  });
});
