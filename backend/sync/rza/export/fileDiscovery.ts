/**
 * File discovery utilities for RZA sync operations
 * Handles finding and selecting the latest RZA export files
 */

import { promises as fs } from 'fs';
import path from 'path';
import { createModuleLogger } from '../../../infrastructure/logger';

const logger = createModuleLogger('rza-file-discovery');

/**
 * Information about a discovered RZA file
 */
export interface RZAFileInfo {
  filePath: string;
  fileName: string;
  timestamp: Date;
  sequenceNumber: number;
  fileSize: number;
}

/**
 * Parsed filename components for RZA files
 */
interface ParsedRZAFilename {
  prefix: string;
  timestamp: Date;
  sequenceNumber: number;
  isValid: boolean;
}

/**
 * Finds the latest RZA full sync file in the specified directory
 * Looks for files matching pattern: artcustord-{YYYYMMDDhhmmss}_{i}.xml
 * 
 * @param syncDirectory - Directory to search for RZA files (default: backend/data/rza/full_sync)
 * @returns Latest RZA file info or null if no valid files found
 */
export async function findLatestRZAFullSyncFile(
  syncDirectory: string = path.join(process.cwd(), 'data/rza/full_sync')
): Promise<RZAFileInfo | null> {
  try {
    logger.info(`Searching for RZA full sync files in: ${syncDirectory}`);

    // Check if directory exists
    try {
      await fs.access(syncDirectory);
    } catch (error) {
      logger.error(`RZA sync directory does not exist: ${syncDirectory}`);
      return null;
    }

    // Read directory contents
    const files = await fs.readdir(syncDirectory);
    logger.info(`Found ${files.length} files in sync directory`);

    // Filter and parse RZA files
    const rzaFiles: RZAFileInfo[] = [];
    
    for (const fileName of files) {
      const filePath = path.join(syncDirectory, fileName);
      
      // Check if it's a file (not directory)
      const stats = await fs.stat(filePath);
      if (!stats.isFile()) {
        continue;
      }

      // Parse filename
      const parsed = parseRZAFilename(fileName);
      if (!parsed.isValid) {
        continue;
      }

      // Create file info
      const fileInfo: RZAFileInfo = {
        filePath,
        fileName,
        timestamp: parsed.timestamp,
        sequenceNumber: parsed.sequenceNumber,
        fileSize: stats.size
      };

      rzaFiles.push(fileInfo);
      logger.debug(`Found valid RZA file: ${fileName}`, {
        timestamp: fileInfo.timestamp.toISOString(),
        sequence: fileInfo.sequenceNumber,
        size: fileInfo.fileSize
      });
    }

    if (rzaFiles.length === 0) {
      logger.warn('No valid RZA full sync files found');
      return null;
    }

    // Sort by timestamp (newest first), then by sequence number (highest first)
    rzaFiles.sort((a, b) => {
      const timeDiff = b.timestamp.getTime() - a.timestamp.getTime();
      if (timeDiff !== 0) {
        return timeDiff;
      }
      return b.sequenceNumber - a.sequenceNumber;
    });

    const latestFile = rzaFiles[0];
    if (!latestFile) {
      logger.warn('No valid RZA full sync files found after filtering');
      return null;
    }

    logger.info(`Selected latest RZA file: ${latestFile.fileName}`, {
      timestamp: latestFile.timestamp.toISOString(),
      sequence: latestFile.sequenceNumber,
      size: latestFile.fileSize
    });

    return latestFile;
  } catch (error) {
    logger.error('Failed to find latest RZA full sync file:', error);
    throw new Error(`File discovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Parses RZA filename to extract timestamp and sequence number
 * Expected format: artcustord-{YYYYMMDDhhmmss}_{i}.xml
 * 
 * @param fileName - Name of the file to parse
 * @returns Parsed filename components
 */
function parseRZAFilename(fileName: string): ParsedRZAFilename {
  const result: ParsedRZAFilename = {
    prefix: '',
    timestamp: new Date(),
    sequenceNumber: 0,
    isValid: false
  };

  try {
    // Check file extension
    if (!fileName.toLowerCase().endsWith('.xml')) {
      return result;
    }

    // Remove .xml extension
    const nameWithoutExt = fileName.slice(0, -4);

    // Expected pattern: artcustord-{YYYYMMDDhhmmss}_{i}
    const pattern = /^artcustord-(\d{14})_(\d+)$/;
    const match = nameWithoutExt.match(pattern);

    if (!match) {
      return result;
    }

    const timestampStr = match[1]!; // YYYYMMDDhhmmss
    const sequenceStr = match[2]!; // sequence number

    // Parse timestamp: YYYYMMDDhhmmss
    const year = parseInt(timestampStr.substring(0, 4), 10);
    const month = parseInt(timestampStr.substring(4, 6), 10) - 1; // Month is 0-based in Date
    const day = parseInt(timestampStr.substring(6, 8), 10);
    const hour = parseInt(timestampStr.substring(8, 10), 10);
    const minute = parseInt(timestampStr.substring(10, 12), 10);
    const second = parseInt(timestampStr.substring(12, 14), 10);

    // Validate date components
    if (year < 2000 || year > 3000 || month < 0 || month > 11 || 
        day < 1 || day > 31 || hour < 0 || hour > 23 || 
        minute < 0 || minute > 59 || second < 0 || second > 59) {
      return result;
    }

    const timestamp = new Date(year, month, day, hour, minute, second);
    const sequenceNumber = parseInt(sequenceStr, 10);

    // Validate sequence number
    if (isNaN(sequenceNumber) || sequenceNumber < 1) {
      return result;
    }

    result.prefix = 'artcustord';
    result.timestamp = timestamp;
    result.sequenceNumber = sequenceNumber;
    result.isValid = true;

    return result;
  } catch (error) {
    logger.debug(`Failed to parse filename: ${fileName}`, error);
    return result;
  }
}

/**
 * Lists all valid RZA full sync files in the directory
 * Useful for debugging and monitoring
 * 
 * @param syncDirectory - Directory to search for RZA files
 * @returns Array of all valid RZA file info, sorted by timestamp (newest first)
 */
export async function listAllRZAFullSyncFiles(
  syncDirectory: string = path.join(process.cwd(), 'backend/data/rza/full_sync')
): Promise<RZAFileInfo[]> {
  try {
    logger.info(`Listing all RZA full sync files in: ${syncDirectory}`);

    // Check if directory exists
    try {
      await fs.access(syncDirectory);
    } catch (error) {
      logger.warn(`RZA sync directory does not exist: ${syncDirectory}`);
      return [];
    }

    // Read directory contents
    const files = await fs.readdir(syncDirectory);
    const rzaFiles: RZAFileInfo[] = [];
    
    for (const fileName of files) {
      const filePath = path.join(syncDirectory, fileName);
      
      // Check if it's a file (not directory)
      const stats = await fs.stat(filePath);
      if (!stats.isFile()) {
        continue;
      }

      // Parse filename
      const parsed = parseRZAFilename(fileName);
      if (!parsed.isValid) {
        continue;
      }

      // Create file info
      const fileInfo: RZAFileInfo = {
        filePath,
        fileName,
        timestamp: parsed.timestamp,
        sequenceNumber: parsed.sequenceNumber,
        fileSize: stats.size
      };

      rzaFiles.push(fileInfo);
    }

    // Sort by timestamp (newest first), then by sequence number (highest first)
    rzaFiles.sort((a, b) => {
      const timeDiff = b.timestamp.getTime() - a.timestamp.getTime();
      if (timeDiff !== 0) {
        return timeDiff;
      }
      return b.sequenceNumber - a.sequenceNumber;
    });

    logger.info(`Found ${rzaFiles.length} valid RZA full sync files`);
    return rzaFiles;
  } catch (error) {
    logger.error('Failed to list RZA full sync files:', error);
    throw new Error(`File listing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Validates that a file exists and is readable
 * 
 * @param filePath - Path to the file to validate
 * @returns True if file exists and is readable
 */
export async function validateRZAFile(filePath: string): Promise<boolean> {
  try {
    const stats = await fs.stat(filePath);
    return stats.isFile() && stats.size > 0;
  } catch (error) {
    logger.debug(`File validation failed for: ${filePath}`, error);
    return false;
  }
}
