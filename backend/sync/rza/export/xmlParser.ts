/**
 * XML parsing utilities for RZA export files
 * Handles parsing of RZA XML files into typed data structures
 */

import { parseStringPromise } from 'xml2js';
import { RzaArticle, RzaTextilartikel } from './models/article.model';
import { RzaCategory } from './models/category.model';
import { RzaGroupDefinition, RzaArtGroup, RzaArtSubGroup } from './models/articleGroup.model';
import { RzaField, RzaPrice, RzaTaxRate, RzaTranslation } from './models/common.model';
import { createModuleLogger } from '../../../infrastructure/logger';

const logger = createModuleLogger('rza-xml-parser');

/**
 * Complete RZA export data structure
 */
export interface RzaExportData {
  articles: RzaArticle[];
  customers: any[]; // Not needed for full sync
  orders: any[]; // Not needed for full sync
  groupDefinition: RzaGroupDefinition;
  categories: RzaCategory[];
  countries: any[]; // Not needed for full sync
}

/**
 * Parses RZA XML file into structured data
 * 
 * @param xmlContent - Raw XML content string
 * @returns Parsed RZA data structure
 */
export async function parseRZAXML(xmlContent: string): Promise<RzaExportData> {
  try {
    const parsed = await parseStringPromise(xmlContent, {
      explicitArray: false,
      mergeAttrs: false,
      normalize: true,
      normalizeTags: false,
      trim: true
    });

    logger.info('Successfully parsed RZA XML structure');

    return {
      articles: extractArticles(parsed),
      customers: [], // Not needed for full sync
      orders: [], // Not needed for full sync
      groupDefinition: extractGroupDefinition(parsed),
      categories: extractCategories(parsed),
      countries: [] // Not needed for full sync
    };
  } catch (error) {
    logger.error('Failed to parse RZA XML:', error);
    throw new Error(`XML parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Extracts articles from parsed XML
 * 
 * @param parsed - Parsed XML object
 * @returns Array of RZA articles
 */
function extractArticles(parsed: any): RzaArticle[] {
  try {
    const articlesData = parsed?.root?.articles?.article;
    if (!articlesData) {
      logger.warn('No articles found in XML');
      return [];
    }

    // Handle both single article and array of articles
    const articles = Array.isArray(articlesData) ? articlesData : [articlesData];
    
    const rzaArticles: RzaArticle[] = articles
      .map((articleData: any) => parseArticle(articleData))
      .filter((article): article is RzaArticle => article !== null);

    logger.info(`Extracted ${rzaArticles.length} articles from XML`);
    return rzaArticles;
  } catch (error) {
    logger.error('Failed to extract articles:', error);
    return [];
  }
}

/**
 * Parses a single article from XML data
 * 
 * @param articleData - Raw article XML data
 * @returns Parsed RZA article or null if invalid
 */
function parseArticle(articleData: any): RzaArticle | null {
  try {
    // Validate required fields
    if (!articleData.ordernumber || !articleData.name || !articleData.rzaArtikelID) {
      logger.warn('Skipping article with missing required fields:', {
        ordernumber: articleData.ordernumber,
        name: articleData.name,
        rzaArtikelID: articleData.rzaArtikelID
      });
      return null;
    }

    // Parse categories
    const categories: number[] = [];
    if (articleData.categories?.category) {
      const categoryData = Array.isArray(articleData.categories.category) 
        ? articleData.categories.category 
        : [articleData.categories.category];
      categories.push(...categoryData.map((cat: any) => parseInt(cat, 10)).filter((id: number) => !isNaN(id)));
    }

    // Parse fields
    const fields: RzaField[] = [];
    if (articleData.fields?.field) {
      const fieldData = Array.isArray(articleData.fields.field) 
        ? articleData.fields.field 
        : [articleData.fields.field];
      
      fieldData.forEach((field: any) => {
        if (field.$ && field.$.fieldnumber) {
          fields.push({
            fieldnumber: parseInt(field.$.fieldnumber, 10),
            value: field._ || field || ''
          });
        }
      });
    }

    // Parse prices
    const prices: RzaPrice[] = [];
    if (articleData.prices?.price) {
      const priceData = Array.isArray(articleData.prices.price) 
        ? articleData.prices.price 
        : [articleData.prices.price];
      
      priceData.forEach((price: any) => {
        if (price.price && price.pricegroup && price.from) {
          const rzaPrice: RzaPrice = {
            price: parseFloat(price.price),
            pricegroup: price.pricegroup,
            from: parseInt(price.from, 10)
          };

          if (price.percent) {
            rzaPrice.percent = parseFloat(price.percent);
          }

          prices.push(rzaPrice);
        }
      });
    }

    // Parse tax rates
    const taxRates: RzaTaxRate[] = [];
    if (articleData.taxRates?.taxRate) {
      const taxRateData = Array.isArray(articleData.taxRates.taxRate) 
        ? articleData.taxRates.taxRate 
        : [articleData.taxRates.taxRate];
      
      taxRateData.forEach((taxRate: any) => {
        if (taxRate.countryID && taxRate.tax) {
          taxRates.push({
            countryID: parseInt(taxRate.countryID, 10),
            tax: parseFloat(taxRate.tax)
          });
        }
      });
    }

    // Parse supplier numbers
    const suppliernumbers: string[] = [];
    if (articleData.suppliernumbers?.suppliernumber) {
      const supplierData = Array.isArray(articleData.suppliernumbers.suppliernumber) 
        ? articleData.suppliernumbers.suppliernumber 
        : [articleData.suppliernumbers.suppliernumber];
      suppliernumbers.push(...supplierData.filter((num: any) => num && num.trim()));
    }

    // Parse translations
    const translations: RzaTranslation[] = [];
    if (articleData.translations?.translation) {
      const translationData = Array.isArray(articleData.translations.translation) 
        ? articleData.translations.translation 
        : [articleData.translations.translation];
      
      translationData.forEach((translation: any) => {
        if (translation.languageId) {
          translations.push({
            languageId: parseInt(translation.languageId, 10),
            name: translation.name || '',
            longdescription: translation.longdescription || ''
          });
        }
      });
    }

    // Parse textile article info
    const textilartikel: RzaTextilartikel = {};
    if (articleData.textilartikel) {
      const textile = articleData.textilartikel;
      if (textile.kennzeichen) textilartikel.kennzeichen = textile.kennzeichen;
      if (textile.textilartikelID) textilartikel.textilartikelID = textile.textilartikelID;
      if (textile.groessentabelleID) textilartikel.groessentabelleID = textile.groessentabelleID;
      if (textile.groessenID) textilartikel.groessenID = textile.groessenID;
      if (textile.farbtabelleID) textilartikel.farbtabelleID = textile.farbtabelleID;
      if (textile.farbenID) textilartikel.farbenID = textile.farbenID;
      if (textile.saisonID) textilartikel.saisonID = textile.saisonID;
      if (textile.preiszuschlag) textilartikel.preiszuschlag = textile.preiszuschlag;
    }

    const article: RzaArticle = {
      ordernumber: articleData.ordernumber,
      rzaArtikelID: parseInt(articleData.rzaArtikelID, 10),
      artGroupID: parseInt(articleData.artGroupID, 10) || 0,
      artSubGroupID: parseInt(articleData.artSubGroupID, 10) || 0,
      categories,
      name: articleData.name,
      ean: articleData.ean || '',
      barcode: articleData.barcode || '',
      unitID: articleData.unitID || 'stk',
      instock: parseInt(articleData.instock, 10) || 0,
      stock: parseInt(articleData.stock, 10) || 0,
      weight: parseFloat(articleData.weight) || 0,
      active: parseInt(articleData.active, 10) || 0,
      onlineshopstatus: parseInt(articleData.onlineshopstatus, 10) || 0,
      description_long: articleData.description_long || '',
      tax: parseFloat(articleData.tax) || 0,
      taxRates,
      suppliernumbers,
      shippingtime: articleData.shippingtime || '',
      fields,
      translations,
      prices,
      textilartikel
    };

    return article;
  } catch (error) {
    logger.error('Failed to parse article:', error);
    return null;
  }
}

/**
 * Extracts group definition from parsed XML
 * 
 * @param parsed - Parsed XML object
 * @returns RZA group definition
 */
function extractGroupDefinition(parsed: any): RzaGroupDefinition {
  try {
    const groupData = parsed?.root?.groupDefinition;
    if (!groupData) {
      logger.warn('No group definition found in XML');
      return { artGroups: [], artSubGroups: [] };
    }

    // Parse article groups
    const artGroups: RzaArtGroup[] = [];
    if (groupData.artGroups?.artGroup) {
      const groupsData = Array.isArray(groupData.artGroups.artGroup) 
        ? groupData.artGroups.artGroup 
        : [groupData.artGroups.artGroup];
      
      groupsData.forEach((group: any) => {
        if (group.ID && group.name) {
          artGroups.push({
            ID: parseInt(group.ID, 10),
            number: parseInt(group.number, 10) || 0,
            name: group.name,
            discounts: [] // Simplified for now
          });
        }
      });
    }

    // Parse article sub-groups
    const artSubGroups: RzaArtSubGroup[] = [];
    if (groupData.artSubGroups?.artSubGroup) {
      const subGroupsData = Array.isArray(groupData.artSubGroups.artSubGroup) 
        ? groupData.artSubGroups.artSubGroup 
        : [groupData.artSubGroups.artSubGroup];
      
      subGroupsData.forEach((subGroup: any) => {
        if (subGroup.ID && subGroup.name) {
          artSubGroups.push({
            ID: parseInt(subGroup.ID, 10),
            number: parseInt(subGroup.number, 10) || 0,
            name: subGroup.name,
            discounts: [] // Simplified for now
          });
        }
      });
    }

    logger.info(`Extracted ${artGroups.length} article groups and ${artSubGroups.length} sub-groups`);
    return { artGroups, artSubGroups };
  } catch (error) {
    logger.error('Failed to extract group definition:', error);
    return { artGroups: [], artSubGroups: [] };
  }
}

/**
 * Extracts categories from parsed XML
 * 
 * @param parsed - Parsed XML object
 * @returns Array of RZA categories
 */
function extractCategories(parsed: any): RzaCategory[] {
  try {
    const categoriesData = parsed?.root?.categories?.category;
    if (!categoriesData) {
      logger.warn('No categories found in XML');
      return [];
    }

    const categories = Array.isArray(categoriesData) ? categoriesData : [categoriesData];
    
    const rzaCategories: RzaCategory[] = categories
      .map((categoryData: any): RzaCategory | null => {
        if (categoryData.ID && categoryData.name) {
          const category: RzaCategory = {
            ID: parseInt(categoryData.ID, 10),
            name: categoryData.name
          };

          if (categoryData.ParentID) {
            category.ParentID = parseInt(categoryData.ParentID, 10);
          }

          if (categoryData.number) {
            category.number = parseInt(categoryData.number, 10);
          }

          return category;
        }
        return null;
      })
      .filter((category): category is RzaCategory => category !== null);

    logger.info(`Extracted ${rzaCategories.length} categories from XML`);
    return rzaCategories;
  } catch (error) {
    logger.error('Failed to extract categories:', error);
    return [];
  }
}
