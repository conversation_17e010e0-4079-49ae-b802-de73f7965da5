/**
 * Unit tests for SyncService
 */

import { SyncService } from '../sync';
import { DatabaseService } from '../database';
import { ExportJob, ImportJob, LagerstandExportJob } from '../../entities';

// Mock the sync operation functions
jest.mock('../../sync/operations/rzaFullSync', () => ({
  performRZAFullSync: jest.fn(),
}));

jest.mock('../../sync/operations/rzaLagerstandSync', () => ({
  performRZALagerstandSync: jest.fn(),
}));

jest.mock('../../sync/operations/woocommerceOrderSync', () => ({
  performWooCommerceOrderSync: jest.fn(),
}));

// Mock the logger
jest.mock('../../infrastructure/logger', () => ({
  createModuleLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  })),
}));

import { performRZAFullSync } from '../../sync/operations/rzaFullSync';
import { performRZALagerstandSync } from '../../sync/operations/rzaLagerstandSync';
import { performWooCommerceOrderSync } from '../../sync/operations/woocommerceOrderSync';

const mockPerformRZAFullSync = performRZAFullSync as jest.MockedFunction<typeof performRZAFullSync>;
const mockPerformRZALagerstandSync = performRZALagerstandSync as jest.MockedFunction<typeof performRZALagerstandSync>;
const mockPerformWooCommerceOrderSync = performWooCommerceOrderSync as jest.MockedFunction<typeof performWooCommerceOrderSync>;

describe('SyncService', () => {
  let syncService: SyncService;
  let mockDatabaseService: jest.Mocked<DatabaseService>;

  beforeEach(() => {
    // Create mock database service
    mockDatabaseService = {
      createExportJob: jest.fn(),
      updateExportJob: jest.fn(),
      createLagerstandExportJob: jest.fn(),
      updateLagerstandExportJob: jest.fn(),
      createImportJob: jest.fn(),
      updateImportJob: jest.fn(),
    } as any;

    syncService = new SyncService(mockDatabaseService);

    // Reset all mocks
    jest.clearAllMocks();
  });

  describe('syncRZAFullToWoocommerce', () => {
    it('should successfully complete a full sync', async () => {
      // Arrange
      const mockExportJob: ExportJob = {
        id: 'test-job-1',
        startedAt: '2023-01-01T00:00:00.000Z',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockCompletedJob: ExportJob = {
        ...mockExportJob,
        completedAt: '2023-01-01T00:05:00.000Z',
      };

      mockDatabaseService.createExportJob.mockResolvedValue(mockExportJob);
      mockDatabaseService.updateExportJob.mockResolvedValue(mockCompletedJob);
      mockPerformRZAFullSync.mockResolvedValue();

      // Act
      const result = await syncService.syncRZAFullToWoocommerce();

      // Assert
      expect(mockDatabaseService.createExportJob).toHaveBeenCalledTimes(1);
      expect(mockPerformRZAFullSync).toHaveBeenCalledTimes(1);
      expect(mockDatabaseService.updateExportJob).toHaveBeenCalledWith(
        mockExportJob.id,
        expect.objectContaining({
          completedAt: expect.any(String),
        })
      );
      expect(result).toEqual(mockCompletedJob);
    });

    it('should handle sync operation errors', async () => {
      // Arrange
      const mockExportJob: ExportJob = {
        id: 'test-job-2',
        startedAt: '2023-01-01T00:00:00.000Z',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const syncError = new Error('Sync operation failed');

      mockDatabaseService.createExportJob.mockResolvedValue(mockExportJob);
      mockPerformRZAFullSync.mockRejectedValue(syncError);

      // Act & Assert
      await expect(syncService.syncRZAFullToWoocommerce()).rejects.toThrow('Sync operation failed');

      expect(mockDatabaseService.createExportJob).toHaveBeenCalledTimes(1);
      expect(mockPerformRZAFullSync).toHaveBeenCalledTimes(1);
      expect(mockDatabaseService.updateExportJob).toHaveBeenCalledWith(
        mockExportJob.id,
        expect.objectContaining({
          completedAt: expect.any(String),
          errorMessage: 'Sync operation failed',
        })
      );
    });
  });

  describe('syncRZALagerstandToWoocommerce', () => {
    it('should successfully complete a lagerstand sync', async () => {
      // Arrange
      const mockLagerstandJob: LagerstandExportJob = {
        id: 'test-lagerstand-job-1',
        startedAt: '2023-01-01T00:00:00.000Z',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockCompletedJob: LagerstandExportJob = {
        ...mockLagerstandJob,
        completedAt: '2023-01-01T00:05:00.000Z',
      };

      mockDatabaseService.createLagerstandExportJob.mockResolvedValue(mockLagerstandJob);
      mockDatabaseService.updateLagerstandExportJob.mockResolvedValue(mockCompletedJob);
      mockPerformRZALagerstandSync.mockResolvedValue();

      // Act
      const result = await syncService.syncRZALagerstandToWoocommerce();

      // Assert
      expect(mockDatabaseService.createLagerstandExportJob).toHaveBeenCalledTimes(1);
      expect(mockPerformRZALagerstandSync).toHaveBeenCalledTimes(1);
      expect(mockDatabaseService.updateLagerstandExportJob).toHaveBeenCalledWith(
        mockLagerstandJob.id,
        expect.objectContaining({
          completedAt: expect.any(String),
        })
      );
      expect(result).toEqual(mockCompletedJob);
    });

    it('should handle lagerstand sync operation errors', async () => {
      // Arrange
      const mockLagerstandJob: LagerstandExportJob = {
        id: 'test-lagerstand-job-2',
        startedAt: '2023-01-01T00:00:00.000Z',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const syncError = new Error('Lagerstand sync failed');

      mockDatabaseService.createLagerstandExportJob.mockResolvedValue(mockLagerstandJob);
      mockPerformRZALagerstandSync.mockRejectedValue(syncError);

      // Act & Assert
      await expect(syncService.syncRZALagerstandToWoocommerce()).rejects.toThrow('Lagerstand sync failed');

      expect(mockDatabaseService.createLagerstandExportJob).toHaveBeenCalledTimes(1);
      expect(mockPerformRZALagerstandSync).toHaveBeenCalledTimes(1);
      expect(mockDatabaseService.updateLagerstandExportJob).toHaveBeenCalledWith(
        mockLagerstandJob.id,
        expect.objectContaining({
          completedAt: expect.any(String),
          errorMessage: 'Lagerstand sync failed',
        })
      );
    });
  });

  describe('syncWoocommerceOrdersToRZA', () => {
    it('should successfully complete an order sync', async () => {
      // Arrange
      const mockImportJob: ImportJob = {
        id: 'test-import-job-1',
        startedAt: '2023-01-01T00:00:00.000Z',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const mockCompletedJob: ImportJob = {
        ...mockImportJob,
        completedAt: '2023-01-01T00:05:00.000Z',
      };

      mockDatabaseService.createImportJob.mockResolvedValue(mockImportJob);
      mockDatabaseService.updateImportJob.mockResolvedValue(mockCompletedJob);
      mockPerformWooCommerceOrderSync.mockResolvedValue();

      // Act
      const result = await syncService.syncWoocommerceOrdersToRZA();

      // Assert
      expect(mockDatabaseService.createImportJob).toHaveBeenCalledTimes(1);
      expect(mockPerformWooCommerceOrderSync).toHaveBeenCalledTimes(1);
      expect(mockDatabaseService.updateImportJob).toHaveBeenCalledWith(
        mockImportJob.id,
        expect.objectContaining({
          completedAt: expect.any(String),
        })
      );
      expect(result).toEqual(mockCompletedJob);
    });

    it('should handle order sync operation errors', async () => {
      // Arrange
      const mockImportJob: ImportJob = {
        id: 'test-import-job-2',
        startedAt: '2023-01-01T00:00:00.000Z',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const syncError = new Error('Order sync failed');

      mockDatabaseService.createImportJob.mockResolvedValue(mockImportJob);
      mockPerformWooCommerceOrderSync.mockRejectedValue(syncError);

      // Act & Assert
      await expect(syncService.syncWoocommerceOrdersToRZA()).rejects.toThrow('Order sync failed');

      expect(mockDatabaseService.createImportJob).toHaveBeenCalledTimes(1);
      expect(mockPerformWooCommerceOrderSync).toHaveBeenCalledTimes(1);
      expect(mockDatabaseService.updateImportJob).toHaveBeenCalledWith(
        mockImportJob.id,
        expect.objectContaining({
          completedAt: expect.any(String),
          errorMessage: 'Order sync failed',
        })
      );
    });
  });
});
