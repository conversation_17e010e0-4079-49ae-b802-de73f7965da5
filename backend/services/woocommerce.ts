import WooCommerceRestApi from '@woocommerce/woocommerce-rest-api';
import { createModuleLogger } from '../infrastructure/logger';

const logger = createModuleLogger('woocommerce');

export interface WooCommerceConfig {
  url: string;
  consumerKey: string;
  consumerSecret: string;
  version?: string;
}

export interface Product {
  id?: number;
  name: string;
  slug?: string;
  type?: string;
  status?: string;
  featured?: boolean;
  catalog_visibility?: string;
  description?: string;
  short_description?: string;
  sku?: string;
  price?: string;
  regular_price?: string;
  sale_price?: string;
  manage_stock?: boolean;
  stock_quantity?: number;
  stock_status?: string;
  categories?: Array<{ id: number; name?: string }>;
  images?: Array<{ src: string; alt?: string }>;
  weight?: string;
  dimensions?: {
    length?: string;
    width?: string;
    height?: string;
  };
  tax_status?: string;
  tax_class?: string;
  meta_data?: Array<{ key: string; value: string }>;
}

export interface Order {
  id: number;
  status: string;
  currency: string;
  total: string;
  date_created: string;
  date_modified: string;
  customer_id: number;
  billing: {
    first_name: string;
    last_name: string;
    email: string;
    phone?: string;
    address_1: string;
    address_2?: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
  };
  shipping: {
    first_name: string;
    last_name: string;
    address_1: string;
    address_2?: string;
    city: string;
    state: string;
    postcode: string;
    country: string;
  };
  line_items: Array<{
    id: number;
    name: string;
    product_id: number;
    variation_id: number;
    quantity: number;
    price: string;
    total: string;
    sku: string;
  }>;
}

export class WooCommerceService {
  private api: WooCommerceRestApi;

  public constructor(config: WooCommerceConfig) {
    logger.info("WooCommerce Config", config, {hello: 'asdf'})
    this.api = new WooCommerceRestApi({
      url: config.url,
      consumerKey: config.consumerKey,
      consumerSecret: config.consumerSecret,
      version:  'wc/v3',
      queryStringAuth: true
    });

    logger.info('WooCommerce API client initialized', {
      url: config.url,
      version: 'wc/v3'
    });
  }

  // Product methods
  public async getProducts(params: any = {}): Promise<Product[]> {
    try {
      const response = await this.api.get('products', params);
      logger.info(`Retrieved ${response.data.length} products`);
      return response.data;
    } catch (error) {
      logger.error('Failed to get products:', error);
      throw error;
    }
  }

  public async getProduct(id: number): Promise<Product> {
    try {
      const response = await this.api.get(`products/${id}`);
      return response.data;
    } catch (error) {
      logger.error(`Failed to get product ${id}:`, error);
      throw error;
    }
  }

  public async createProduct(product: Product): Promise<Product> {
    try {
      const response = await this.api.post('products', product);
      logger.info(`Created product: ${response.data.name} (ID: ${response.data.id})`);
      return response.data;
    } catch (error) {
      logger.error('Failed to create product:', error);
      throw error;
    }
  }

  public async updateProduct(id: number, product: Partial<Product>): Promise<Product> {
    try {
      const response = await this.api.put(`products/${id}`, product);
      logger.info(`Updated product ID: ${id}`);
      return response.data;
    } catch (error) {
      logger.error(`Failed to update product ${id}:`, error);
      throw error;
    }
  }

  public async deleteProduct(id: number): Promise<void> {
    try {
      await this.api.delete(`products/${id}`, { force: true });
      logger.info(`Deleted product ID: ${id}`);
    } catch (error) {
      logger.error(`Failed to delete product ${id}:`, error);
      throw error;
    }
  }

  // Order methods
  public async getOrders(params: any = {}): Promise<Order[]> {
    try {
      const response = await this.api.get('orders', params);
      logger.info(`Retrieved ${response.data.length} orders`);
      return response.data;
    } catch (error) {
      logger.error('Failed to get orders:', error);
      throw error;
    }
  }

  public async getOrder(id: number): Promise<Order> {
    try {
      const response = await this.api.get(`orders/${id}`);
      return response.data;
    } catch (error) {
      logger.error(`Failed to get order ${id}:`, error);
      throw error;
    }
  }

  public async updateOrderStatus(id: number, status: string): Promise<Order> {
    try {
      const response = await this.api.put(`orders/${id}`, { status });
      logger.info(`Updated order ${id} status to: ${status}`);
      return response.data;
    } catch (error) {
      logger.error(`Failed to update order ${id} status:`, error);
      throw error;
    }
  }

  // Health check
  public async healthCheck(): Promise<boolean> {
    try {
      await this.api.get('system_status');
      return true;
    } catch (error) {
      console.error('WooCommerce health check failed:', error);
      logger.error('WooCommerce health check failed:', error);
      return false;
    }
  }
}

