import { Export<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>gerstandExportJob } from "@/entities";
import { createModuleLogger } from "../infrastructure/logger";
import { performRZAFullSync } from "../sync/operations/rzaFullSync";
import { performRZALagerstandSync } from "../sync/operations/rzaLagerstandSync";
import { performWooCommerceOrderSync } from "../sync/operations/woocommerceOrderSync";
import { DatabaseService } from "./database";
import { WooCommerceService } from "./woocommerce";

const logger = createModuleLogger("sync-service");

interface ISyncService {
  syncRZAFullToWoocommerce(): Promise<ExportJob>;
  syncRZALagerstandToWoocommerce(): Promise<LagerstandExportJob>;
  syncWoocommerceOrdersToRZA(): Promise<ImportJob>;
}

export class SyncService implements ISyncService {

  constructor(
    private databaseService: DatabaseService,
    private wooCommerceService: WooCommerceService
  ) {
  }

  /**
   * Performs a full sync of all products from RZA to WooCommerce
   * Creates an ExportJob to track the operation
   */
  async syncRZAFullToWoocommerce(): Promise<ExportJob> {
    logger.info("Starting RZA full sync to WooCommerce");

    // Create a new export job in the database
    const exportJob = await this.databaseService.createExportJob();

    try {
      // Perform the actual sync operation
      await performRZAFullSync(this.wooCommerceService);

      // Mark the job as completed
      const completedJob = await this.databaseService.updateExportJob(
        exportJob.id,
        {
          completedAt: new Date().toISOString(),
        }
      );

      logger.info("RZA full sync completed successfully", {
        jobId: exportJob.id,
      });
      return completedJob;
    } catch (error) {
      // Mark the job as failed with error message
      await this.databaseService.updateExportJob(exportJob.id, {
        completedAt: new Date().toISOString(),
        errorMessage:
          error instanceof Error ? error.message : "Unknown error occurred",
      });

      logger.error("RZA full sync failed", { jobId: exportJob.id, error });
      throw error;
    }
  }

  /**
   * Performs a stock-only sync from RZA to WooCommerce
   * Creates a LagerstandExportJob to track the operation
   */
  async syncRZALagerstandToWoocommerce(): Promise<LagerstandExportJob> {
    logger.info("Starting RZA lagerstand sync to WooCommerce");

    // Create a new lagerstand export job in the database
    const lagerstandJob =
      await this.databaseService.createLagerstandExportJob();

    try {
      // Perform the actual stock sync operation
      await performRZALagerstandSync();

      // Mark the job as completed
      const completedJob = await this.databaseService.updateLagerstandExportJob(
        lagerstandJob.id,
        {
          completedAt: new Date().toISOString(),
        }
      );

      logger.info("RZA lagerstand sync completed successfully", {
        jobId: lagerstandJob.id,
      });
      return completedJob;
    } catch (error) {
      // Mark the job as failed with error message
      await this.databaseService.updateLagerstandExportJob(lagerstandJob.id, {
        completedAt: new Date().toISOString(),
        errorMessage:
          error instanceof Error ? error.message : "Unknown error occurred",
      });

      logger.error("RZA lagerstand sync failed", {
        jobId: lagerstandJob.id,
        error,
      });
      throw error;
    }
  }

  /**
   * Performs synchronization of orders from WooCommerce to RZA
   * Creates an ImportJob to track the operation
   */
  async syncWoocommerceOrdersToRZA(): Promise<ImportJob> {
    logger.info("Starting WooCommerce orders sync to RZA");

    // Create a new import job in the database
    const importJob = await this.databaseService.createImportJob();

    try {
      // Perform the actual order sync operation
      await performWooCommerceOrderSync();

      // Mark the job as completed
      const completedJob = await this.databaseService.updateImportJob(
        importJob.id,
        {
          completedAt: new Date().toISOString(),
        }
      );

      logger.info("WooCommerce orders sync completed successfully", {
        jobId: importJob.id,
      });
      return completedJob;
    } catch (error) {
      // Mark the job as failed with error message
      await this.databaseService.updateImportJob(importJob.id, {
        completedAt: new Date().toISOString(),
        errorMessage:
          error instanceof Error ? error.message : "Unknown error occurred",
      });

      logger.error("WooCommerce orders sync failed", {
        jobId: importJob.id,
        error,
      });
      throw error;
    }
  }
}
