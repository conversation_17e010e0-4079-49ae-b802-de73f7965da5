/**
 * Express server with sync endpoints and cron jobs
 * Provides REST API endpoints to trigger sync operations manually
 * Runs automated cron jobs for scheduled synchronization
 */

import "reflect-metadata"
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { CronJob } from 'cron';
import dotenv from 'dotenv';

import { DatabaseService } from '../services/database';
import { SyncService } from '../services/sync';
import { createModuleLogger } from '../infrastructure/logger';
import { WooCommerceService } from '@/services/woocommerce';

// Load environment variables
dotenv.config();

const logger = createModuleLogger('server');
const app = express();
const PORT = process.env.PORT
  ? parseInt(process.env.PORT)
  : 3001;

// Middleware
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json());

// Global services
let databaseService: DatabaseService;
let syncService: SyncService;
let wooCommerceService: WooCommerceService;

async function initializeServices(): Promise<void> {
  try {
    // Initialize database service first
    databaseService = new DatabaseService();
    await databaseService.initialize();
    logger.info('Database service initialized');

    wooCommerceService = new WooCommerceService({
      url: process.env.WOOCOMMERCE_URL!,
      consumerKey: process.env.WOOCOMMERCE_CONSUMER_KEY!,
      consumerSecret: process.env.WOOCOMMERCE_CONSUMER_SECRET!,
      version: 'wc/v3'
    });
    logger.info('WooCommerce service initialized');

    // Initialize sync service with database dependency
    syncService = new SyncService(databaseService, wooCommerceService);
    logger.info('Sync service initialized');

  } catch (error) {
    logger.error('Failed to initialize services:', error);
    throw error;
  }
}

/**
 * Setup cron jobs for automated synchronization
 */
function setupCronJobs(): void {
  const rzaFullSyncCron = process.env.CRON_RZA_FULL_SYNC || '0 0 2 * * *';
  const rzaLagerstandSyncCron = process.env.CRON_RZA_LAGERSTAND_SYNC || '0 */30 * * * *';
  const woocommerceOrderSyncCron = process.env.CRON_WOOCOMMERCE_ORDER_SYNC || '0 */15 * * * *';

  // RZA Full Sync Cron Job
  const rzaFullSyncJob = new CronJob(
    rzaFullSyncCron,
    async () => {
      try {
        logger.info('Starting scheduled RZA full sync');
        await syncService.syncRZAFullToWoocommerce();
        logger.info('Scheduled RZA full sync completed');
      } catch (error) {
        logger.error('Scheduled RZA full sync failed:', error);
      }
    },
    null,
    true, // Start immediately
    'UTC'
  );

  // RZA Lagerstand Sync Cron Job
  const rzaLagerstandSyncJob = new CronJob(
    rzaLagerstandSyncCron,
    async () => {
      try {
        logger.info('Starting scheduled RZA lagerstand sync');
        await syncService.syncRZALagerstandToWoocommerce();
        logger.info('Scheduled RZA lagerstand sync completed');
      } catch (error) {
        logger.error('Scheduled RZA lagerstand sync failed:', error);
      }
    },
    null,
    true, // Start immediately
    'UTC'
  );

  // WooCommerce Order Sync Cron Job
  const woocommerceOrderSyncJob = new CronJob(
    woocommerceOrderSyncCron,
    async () => {
      try {
        logger.info('Starting scheduled WooCommerce order sync');
        await syncService.syncWoocommerceOrdersToRZA();
        logger.info('Scheduled WooCommerce order sync completed');
      } catch (error) {
        logger.error('Scheduled WooCommerce order sync failed:', error);
      }
    },
    null,
    true, // Start immediately
    'UTC'
  );

  logger.info('Cron jobs configured:', {
    rzaFullSync: rzaFullSyncCron,
    rzaLagerstandSync: rzaLagerstandSyncCron,
    woocommerceOrderSync: woocommerceOrderSyncCron
  });
}

// API Routes

app.get('/hello', (req, res) => {
  res.send('Hello World!');
})

/**
 * Trigger RZA full sync manually
 */
app.post('/api/sync/rza-full', async (req, res) => {
  try {
    logger.info('Manual RZA full sync triggered');
    const result = await syncService.syncRZAFullToWoocommerce();
    res.json({
      success: true,
      message: 'RZA full sync completed',
      jobId: result.id
    });
  } catch (error) {
    logger.error('Manual RZA full sync failed:', error);
    res.status(500).json({
      success: false,
      message: 'RZA full sync failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Trigger RZA lagerstand sync manually
 */
app.post('/api/sync/rza-lagerstand', async (req, res) => {
  try {
    logger.info('Manual RZA lagerstand sync triggered');
    const result = await syncService.syncRZALagerstandToWoocommerce();
    res.json({
      success: true,
      message: 'RZA lagerstand sync completed',
      jobId: result.id
    });
  } catch (error) {
    logger.error('Manual RZA lagerstand sync failed:', error);
    res.status(500).json({
      success: false,
      message: 'RZA lagerstand sync failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Trigger WooCommerce order sync manually
 */
app.post('/api/sync/woocommerce-orders', async (req, res) => {
  try {
    logger.info('Manual WooCommerce order sync triggered');
    const result = await syncService.syncWoocommerceOrdersToRZA();
    res.json({
      success: true,
      message: 'WooCommerce order sync completed',
      jobId: result.id
    });
  } catch (error) {
    logger.error('Manual WooCommerce order sync failed:', error);
    res.status(500).json({
      success: false,
      message: 'WooCommerce order sync failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Start the server
 */
async function startServer(): Promise<void> {
  try {
    // Initialize services first
    await initializeServices();

    // Setup cron jobs
    setupCronJobs();

    // Start Express server
    app.listen(PORT, '0.0.0.0', () => {
      logger.info(`Server running on port ${PORT}`);
      logger.info('Available endpoints:');
      logger.info('  POST /api/sync/rza-full - Trigger RZA full sync');
      logger.info('  POST /api/sync/rza-lagerstand - Trigger RZA lagerstand sync');
      logger.info('  POST /api/sync/woocommerce-orders - Trigger WooCommerce order sync');
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  if (databaseService) {
    await databaseService.close();
  }
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  if (databaseService) {
    await databaseService.close();
  }
  process.exit(0);
});

// Start the server
startServer().catch((error) => {
  logger.error('Failed to start server:', error);
  process.exit(1);
});